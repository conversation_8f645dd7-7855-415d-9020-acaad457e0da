# LLM Memory Test Application

## Overview

This application tests different LLM memory implementations by simulating conversations between two
LLMs (user and assistant) and evaluating memory retention. The application injects facts into the
conversation and later tests if the LLM can recall these facts.

## Features

- Simulates conversations between user and assistant LLMs
- Supports two memory implementations:
  - Simple memory (keeps last N messages)
  - Summary memory with knowledge extraction (summarizes older messages and extracts important
    facts)
- Evaluates memory retention with scoring
- Saves test results to markdown files
- Supports both OpenRouter and OpenAI APIs

## Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Copy `.env.example` to `.env` and configure your settings:
   ```
   cp .env.example .env
   ```
4. Edit the `.env` file with your API keys and configuration

## Configuration

The application is configured through the `.env` file:

### API Credentials

- `OPENROUTER_API_KEY`: Your OpenRouter API key (default)
- `OPENAI_API_KEY`: Your OpenAI API key (alternative)

### LLM Models

- `USER_MODEL`: Model for simulating user (e.g., openai/gpt-3.5-turbo)
- `ASSISTANT_MODEL`: Model for simulating assistant (e.g., openai/gpt-3.5-turbo)
- `EVALUATOR_MODEL`: Model for evaluating responses (e.g., openai/gpt-3.5-turbo)
- `SUMMARY_MODEL`: Model for generating summaries (e.g., openai/gpt-3.5-turbo)
- `KNOWLEDGE_EXTRACTION_MODEL`: Model for extracting knowledge (e.g., openai/gpt-3.5-turbo)

### Memory Configuration

- `MEMORY_TYPE`: Type of memory to use (simple, summary, summary_with_knowledge)
- `MEMORY_CONTEXT_WINDOW`: Number of messages to keep in simple memory
- `SUMMARY_THRESHOLD`: Number of messages before summarization
- `ENABLE_KNOWLEDGE_EXTRACTION`: Whether to extract knowledge in summary memory

### Test Configuration

- `TEST_FACTS_FILE`: Which test facts file to use (casual, flirty, technical, medical, travel,
  events, simple)
- `TEST_FACTS_COUNT`: Number of facts to test from the JSON file
- `MESSAGES_BETWEEN_FACTS`: Number of conversation messages between facts
- `SAVE_RESULTS`: Whether to save results to file
- `VERBOSE_LOGGING`: Enable detailed logging

## Usage

Run the application:

```
node src/app.js
```

## Project Structure

- `src/app.js`: Main application entry point
- `src/memory/`: Memory implementations
  - `memoryInterface.js`: Interface for memory implementations
  - `memoryFactory.js`: Factory for creating memory instances
  - `simpleMemory.js`: Simple memory implementation
  - `inMemorySummaryMemory.js`: Summary memory with knowledge extraction
- `src/utils/`: Utility modules
  - `conversationSimulator.js`: Conversation simulation
  - `dataLoader.js`: Data loading utilities
  - `evaluator.js`: Memory evaluation utilities
  - `prompts.js`: System prompts for different LLM roles

- `data/`: Data files
  - `test_facts_*.json`: Test facts for memory evaluation

## Results

Test results are saved to markdown files with the format
`results_[timestamp]_[memoryType]_[scenario]_[sessionId].md`. These files contain:

- Test configuration details
- Evaluation results for each fact
- Overall memory retention score
- Separate scores for simple vs. complex facts
- Complete conversation log
- Memory contexts (if verbose logging is enabled)

## Extending the Framework

This project is designed as a basic framework that can be easily extended to test your own memory
implementations. Here are some ways you can extend it:

### Adding New Memory Models

1. Create a new memory implementation in the `src/memory/` directory
2. Implement the interface defined in `memoryInterface.js`
3. Register your new memory type in `memoryFactory.js`
4. Update the `.env` file to use your new memory type

Example of creating a new memory implementation:

```javascript
// src/memory/myCustomMemory.js
const MemoryInterface = require('./memoryInterface');

class MyCustomMemory extends MemoryInterface {
  constructor(config) {
    super();
    this.config = config;
    // Initialize your memory structure
  }

  // Implement required methods
  addMessage(message) {
    // Your implementation
  }

  getContext() {
    // Your implementation
  }

  // Add any additional methods specific to your implementation
}

module.exports = MyCustomMemory;
```

Then register it in the factory:

```javascript
// In memoryFactory.js
case 'my_custom':
  return new MyCustomMemory(config);
```

### Adding New Test Facts

1. Create a new JSON file in the `data/` directory (e.g., `test_facts_custom.json`)
2. Follow the existing format with simple and complex facts
3. Update the `.env` file to use your new test facts file (`TEST_FACTS_FILE=custom`)

Example test facts file structure:

```json
{
  "simple": [
    {
      "fact": "Your simple fact here",
      "question": "Question to test recall of the fact",
      "answer": "Expected answer"
    }
  ],
  "complex": [
    {
      "fact": "Your complex fact here with more details",
      "question": "Question to test recall of the complex fact",
      "answer": "Expected answer"
    }
  ]
}
```

### Customizing Evaluation Logic

You can modify the evaluation process by updating the `src/utils/evaluator.js` file to implement
different scoring mechanisms or evaluation criteria.

### Adding New LLM Providers

The framework currently supports OpenRouter and OpenAI. To add support for other LLM providers:

1. Update the API client in the relevant files
2. Add appropriate configuration options in the `.env` file
3. Modify the prompt templates in `src/utils/prompts.js` if needed

## License

MIT License
