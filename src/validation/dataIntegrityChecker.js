/**
 * Data Integrity and Format Checking Utilities
 * 
 * This module provides utilities for checking data integrity, format validation,
 * and data migration support for the LLM Memory Test Application.
 */

const fs = require('fs').promises;
const path = require('path');
const SchemaValidator = require('./schemaValidator');

/**
 * Data integrity error class
 */
class DataIntegrityError extends Error {
  constructor(message, details = {}) {
    super(message);
    this.name = 'DataIntegrityError';
    this.details = details;
  }
}

/**
 * Data Integrity Checker class
 */
class DataIntegrityChecker {
  constructor() {
    this.validator = new SchemaValidator();
  }

  /**
   * Validate test fact file format and integrity on load
   * @param {string} filePath - Path to the test facts file
   * @returns {Promise<Object>} Validation result with integrity checks
   */
  async validateTestFactsFileIntegrity(filePath) {
    try {
      // Basic file existence and format validation
      const validationResult = await await this.readAndValidateTestFactsFile(filePath);
      
      if (!validationResult.isValid) {
        return validationResult;
      }

      const facts = validationResult.data;
      const integrityChecks = {
        duplicateIds: this.checkDuplicateIds(facts),
        sequentialIds: this.checkSequentialIds(facts),
        contentIntegrity: this.checkContentIntegrity(facts),
        crossReferences: this.checkCrossReferences(facts),
        dataConsistency: this.checkDataConsistency(facts)
      };

      // Add integrity warnings to the result
      const integrityWarnings = [];
      Object.entries(integrityChecks).forEach(([check, result]) => {
        if (result.issues.length > 0) {
          integrityWarnings.push(...result.issues.map(issue => `${check}: ${issue}`));
        }
      });

      return {
        ...validationResult,
        integrityChecks,
        warnings: [...validationResult.warnings, ...integrityWarnings]
      };

    } catch (error) {
      throw new DataIntegrityError(`Failed to validate test facts file integrity: ${error.message}`, {
        filePath,
        originalError: error
      });
    }
  }

  /**
   * Check conversation log integrity during simulation
   * @param {Array} conversationLog - Conversation log entries
   * @param {Object} expectedFlow - Expected conversation flow parameters
   * @returns {Object} Integrity check result
   */
  validateConversationLogIntegrity(conversationLog, expectedFlow = {}) {
    const integrityResult = {
      isValid: true,
      issues: [],
      warnings: [],
      statistics: {
        totalMessages: conversationLog.length,
        userMessages: 0,
        assistantMessages: 0,
        averageMessageLength: 0,
        conversationDuration: null
      }
    };

    try {
      // Basic schema validation
      const schemaResult = this.validator.validateConversationLog(conversationLog);
      if (!schemaResult.isValid) {
        integrityResult.isValid = false;
        integrityResult.issues.push(...schemaResult.errors.map(e => e.message));
      }

      // Calculate statistics
      let totalLength = 0;
      let firstTimestamp = null;
      let lastTimestamp = null;

      for (const entry of conversationLog) {
        if (entry.role === 'user') {
          integrityResult.statistics.userMessages++;
        } else if (entry.role === 'assistant') {
          integrityResult.statistics.assistantMessages++;
        }

        totalLength += entry.content.length;

        if (entry.timestamp) {
          const timestamp = new Date(entry.timestamp);
          if (!firstTimestamp || timestamp < firstTimestamp) {
            firstTimestamp = timestamp;
          }
          if (!lastTimestamp || timestamp > lastTimestamp) {
            lastTimestamp = timestamp;
          }
        }
      }

      integrityResult.statistics.averageMessageLength = totalLength / conversationLog.length;
      
      if (firstTimestamp && lastTimestamp) {
        integrityResult.statistics.conversationDuration = lastTimestamp - firstTimestamp;
      }

      // Check conversation flow integrity
      this.checkConversationFlow(conversationLog, integrityResult, expectedFlow);

      // Check for potential data corruption
      this.checkConversationDataCorruption(conversationLog, integrityResult);

      // Check timing consistency
      this.checkConversationTiming(conversationLog, integrityResult);

    } catch (error) {
      integrityResult.isValid = false;
      integrityResult.issues.push(`Integrity check failed: ${error.message}`);
    }

    return integrityResult;
  }

  /**
   * Validate result output format before saving
   * @param {Object} testResults - Test results object
   * @param {string} outputPath - Intended output path
   * @returns {Object} Format validation result
   */
  validateResultOutputFormat(testResults, outputPath) {
    const formatResult = {
      isValid: true,
      issues: [],
      warnings: [],
      recommendations: []
    };

    try {
      // Schema validation
      const schemaResult = this.validator.validateTestResults(testResults);
      if (!schemaResult.isValid) {
        formatResult.isValid = false;
        formatResult.issues.push(...schemaResult.errors.map(e => e.message));
      }
      formatResult.warnings.push(...schemaResult.warnings);

      // Check output path validity
      const pathValidation = this.validateOutputPath(outputPath);
      if (!pathValidation.isValid) {
        formatResult.isValid = false;
        formatResult.issues.push(...pathValidation.issues);
      }

      // Check data completeness
      this.checkResultCompleteness(testResults, formatResult);

      // Check data consistency
      this.checkResultConsistency(testResults, formatResult);

      // Generate format recommendations
      formatResult.recommendations = this.generateFormatRecommendations(testResults, outputPath);

    } catch (error) {
      formatResult.isValid = false;
      formatResult.issues.push(`Format validation failed: ${error.message}`);
    }

    return formatResult;
  }

  /**
   * Check for duplicate IDs in test facts
   * @param {Array} facts - Array of test facts
   * @returns {Object} Check result
   */
  checkDuplicateIds(facts) {
    const result = { isValid: true, issues: [] };
    const seenIds = new Set();
    const duplicates = new Set();

    for (const fact of facts) {
      if (seenIds.has(fact.id)) {
        duplicates.add(fact.id);
        result.isValid = false;
      }
      seenIds.add(fact.id);
    }

    if (duplicates.size > 0) {
      result.issues.push(`Duplicate IDs found: ${Array.from(duplicates).join(', ')}`);
    }

    return result;
  }

  /**
   * Check if IDs are sequential (warning, not error)
   * @param {Array} facts - Array of test facts
   * @returns {Object} Check result
   */
  checkSequentialIds(facts) {
    const result = { isValid: true, issues: [] };
    const ids = facts.map(f => f.id).sort((a, b) => a - b);
    
    let expectedId = ids[0];
    for (const id of ids) {
      if (id !== expectedId) {
        result.issues.push(`Non-sequential ID detected: expected ${expectedId}, found ${id}`);
      }
      expectedId++;
    }

    return result;
  }

  /**
   * Check content integrity (encoding, special characters, etc.)
   * @param {Array} facts - Array of test facts
   * @returns {Object} Check result
   */
  checkContentIntegrity(facts) {
    const result = { isValid: true, issues: [] };

    for (const fact of facts) {
      // Check for potential encoding issues
      if (this.hasEncodingIssues(fact.fact)) {
        result.issues.push(`Potential encoding issue in fact ${fact.id}`);
      }

      // Check for suspicious patterns
      if (this.hasSuspiciousPatterns(fact.fact)) {
        result.issues.push(`Suspicious content pattern in fact ${fact.id}`);
      }

      // Check question-answer alignment
      if (!this.isQuestionAnswerAligned(fact.question, fact.answer, fact.fact)) {
        result.issues.push(`Question-answer misalignment in fact ${fact.id}`);
      }
    }

    return result;
  }

  /**
   * Check cross-references between facts
   * @param {Array} facts - Array of test facts
   * @returns {Object} Check result
   */
  checkCrossReferences(facts) {
    const result = { isValid: true, issues: [] };

    // Check for conflicting information
    const conflicts = this.findConflictingFacts(facts);
    if (conflicts.length > 0) {
      result.issues.push(...conflicts.map(c => `Conflicting facts: ${c.fact1} vs ${c.fact2} - ${c.reason}`));
    }

    // Check for redundant information
    const redundancies = this.findRedundantFacts(facts);
    if (redundancies.length > 0) {
      result.issues.push(...redundancies.map(r => `Redundant facts: ${r.fact1} and ${r.fact2}`));
    }

    return result;
  }

  /**
   * Check data consistency across fields
   * @param {Array} facts - Array of test facts
   * @returns {Object} Check result
   */
  checkDataConsistency(facts) {
    const result = { isValid: true, issues: [] };

    for (const fact of facts) {
      // Check complexity assignment consistency
      if (!this.isComplexityConsistent(fact)) {
        result.issues.push(`Inconsistent complexity assignment for fact ${fact.id}`);
      }

      // Check answer length vs complexity
      if (!this.isAnswerLengthAppropriate(fact)) {
        result.issues.push(`Answer length inappropriate for complexity in fact ${fact.id}`);
      }
    }

    return result;
  }

  /**
   * Check conversation flow integrity
   * @param {Array} conversationLog - Conversation log
   * @param {Object} result - Result object to update
   * @param {Object} expectedFlow - Expected flow parameters
   */
  checkConversationFlow(conversationLog, result, expectedFlow) {
    let expectedCycle = 1;
    let expectedRole = 'user';

    for (let i = 0; i < conversationLog.length; i++) {
      const entry = conversationLog[i];

      // Check cycle progression
      if (entry.cycle !== expectedCycle) {
        result.issues.push(`Cycle mismatch at index ${i}: expected ${expectedCycle}, got ${entry.cycle}`);
        result.isValid = false;
      }

      // Check role alternation
      if (entry.role !== expectedRole) {
        result.warnings.push(`Role alternation issue at cycle ${entry.cycle}: expected ${expectedRole}, got ${entry.role}`);
      }

      // Update expectations
      expectedCycle++;
      expectedRole = expectedRole === 'user' ? 'assistant' : 'user';
    }

    // Check expected message count
    if (expectedFlow.expectedMessages && conversationLog.length !== expectedFlow.expectedMessages) {
      result.warnings.push(`Message count mismatch: expected ${expectedFlow.expectedMessages}, got ${conversationLog.length}`);
    }
  }

  /**
   * Check for potential data corruption in conversation
   * @param {Array} conversationLog - Conversation log
   * @param {Object} result - Result object to update
   */
  checkConversationDataCorruption(conversationLog, result) {
    for (let i = 0; i < conversationLog.length; i++) {
      const entry = conversationLog[i];

      // Check for truncated messages
      if (this.isTruncatedMessage(entry.content)) {
        result.warnings.push(`Potentially truncated message at cycle ${entry.cycle}`);
      }

      // Check for repeated content
      if (i > 0 && entry.content === conversationLog[i - 1].content) {
        result.warnings.push(`Repeated content at cycle ${entry.cycle}`);
      }

      // Check for encoding issues
      if (this.hasEncodingIssues(entry.content)) {
        result.issues.push(`Encoding issue detected at cycle ${entry.cycle}`);
        result.isValid = false;
      }
    }
  }

  /**
   * Check conversation timing consistency
   * @param {Array} conversationLog - Conversation log
   * @param {Object} result - Result object to update
   */
  checkConversationTiming(conversationLog, result) {
    for (let i = 1; i < conversationLog.length; i++) {
      const current = conversationLog[i];
      const previous = conversationLog[i - 1];

      if (current.timestamp && previous.timestamp) {
        const currentTime = new Date(current.timestamp);
        const previousTime = new Date(previous.timestamp);

        // Check for backwards time
        if (currentTime < previousTime) {
          result.issues.push(`Timestamp regression at cycle ${current.cycle}`);
          result.isValid = false;
        }

        // Check for unrealistic time gaps
        const timeDiff = currentTime - previousTime;
        if (timeDiff > 300000) { // 5 minutes
          result.warnings.push(`Large time gap (${Math.round(timeDiff / 1000)}s) between cycles ${previous.cycle} and ${current.cycle}`);
        }
      }
    }
  }

  /**
   * Validate output path
   * @param {string} outputPath - Output file path
   * @returns {Object} Validation result
   */
  validateOutputPath(outputPath) {
    const result = { isValid: true, issues: [] };

    try {
      // Check path format
      const parsedPath = path.parse(outputPath);
      
      if (!parsedPath.ext) {
        result.issues.push('Output path should include file extension');
        result.isValid = false;
      }

      if (parsedPath.name.length === 0) {
        result.issues.push('Output path should include filename');
        result.isValid = false;
      }

      // Check for invalid characters
      const invalidChars = /[<>:"|?*]/;
      if (invalidChars.test(outputPath)) {
        result.issues.push('Output path contains invalid characters');
        result.isValid = false;
      }

      // Check path length (Windows limitation)
      if (outputPath.length > 260) {
        result.issues.push('Output path is too long (>260 characters)');
        result.isValid = false;
      }

    } catch (error) {
      result.issues.push(`Invalid output path: ${error.message}`);
      result.isValid = false;
    }

    return result;
  }

  /**
   * Check result completeness
   * @param {Object} testResults - Test results object
   * @param {Object} result - Result object to update
   */
  checkResultCompleteness(testResults, result) {
    const required = ['metadata', 'scores', 'evaluationResults', 'conversationLog'];
    
    for (const field of required) {
      if (!testResults[field]) {
        result.issues.push(`Missing required field: ${field}`);
        result.isValid = false;
      }
    }

    // Check evaluation results completeness
    if (testResults.evaluationResults) {
      const factIds = new Set();
      for (const evalResult of testResults.evaluationResults) {
        if (factIds.has(evalResult.factId)) {
          result.warnings.push(`Duplicate evaluation for fact ${evalResult.factId}`);
        }
        factIds.add(evalResult.factId);
      }
    }
  }

  /**
   * Check result consistency
   * @param {Object} testResults - Test results object
   * @param {Object} result - Result object to update
   */
  checkResultConsistency(testResults, result) {
    if (!testResults.metadata || !testResults.evaluationResults) {
      return;
    }

    // Check fact count consistency
    const expectedFacts = testResults.metadata.factsTested;
    const actualResults = testResults.evaluationResults.length;
    
    if (expectedFacts !== actualResults) {
      result.warnings.push(`Fact count inconsistency: metadata says ${expectedFacts}, got ${actualResults} results`);
    }

    // Check score calculation consistency
    if (testResults.scores && testResults.evaluationResults.length > 0) {
      const scores = testResults.evaluationResults.map(r => r.score);
      const calculatedOverall = (scores.reduce((sum, score) => sum + score, 0) / scores.length) * 10;
      
      if (Math.abs(calculatedOverall - testResults.scores.overall) > 0.1) {
        result.warnings.push(`Score calculation inconsistency: calculated ${calculatedOverall.toFixed(2)}%, reported ${testResults.scores.overall}%`);
      }
    }
  }

  /**
   * Generate format recommendations
   * @param {Object} testResults - Test results object
   * @param {string} outputPath - Output path
   * @returns {Array} Recommendations
   */
  generateFormatRecommendations(testResults, outputPath) {
    const recommendations = [];

    // File format recommendations
    const ext = path.extname(outputPath).toLowerCase();
    if (ext === '.json') {
      recommendations.push('Consider using .md format for better readability');
    }

    // Data structure recommendations
    if (!testResults.performanceMetrics) {
      recommendations.push('Include performance metrics for comprehensive analysis');
    }

    if (!testResults.memoryContexts) {
      recommendations.push('Include memory contexts for debugging and analysis');
    }

    // Metadata recommendations
    if (testResults.metadata && !testResults.metadata.sessionId) {
      recommendations.push('Include session ID for result tracking');
    }

    return recommendations;
  }

  /**
   * Helper methods for content analysis
   */

  hasEncodingIssues(text) {
    // Check for common encoding issues
    return /[\uFFFD\u0000-\u0008\u000B\u000C\u000E-\u001F]/.test(text);
  }

  hasSuspiciousPatterns(text) {
    // Check for patterns that might indicate data corruption
    const suspiciousPatterns = [
      /(.)\1{10,}/, // Repeated character 10+ times
      /^[A-Z\s]{50,}$/, // All caps text over 50 chars (reduced threshold)
      /\b(test|example|placeholder|lorem|ipsum)\b/i // Placeholder text
    ];
    
    return suspiciousPatterns.some(pattern => pattern.test(text));
  }

  isQuestionAnswerAligned(question, answer, fact) {
    // Basic alignment check - this could be enhanced with NLP
    const questionWords = question.toLowerCase().split(/\W+/).filter(w => w.length > 2);
    const answerWords = answer.toLowerCase().split(/\W+/).filter(w => w.length > 2);
    const factWords = fact.toLowerCase().split(/\W+/).filter(w => w.length > 2);
    
    // Check if answer words appear in the fact
    const answerInFact = answerWords.some(word => factWords.includes(word));
    
    return answerInFact;
  }

  findConflictingFacts(facts) {
    // Simple conflict detection - could be enhanced
    const conflicts = [];
    
    for (let i = 0; i < facts.length; i++) {
      for (let j = i + 1; j < facts.length; j++) {
        const fact1 = facts[i];
        const fact2 = facts[j];
        
        // Check for direct contradictions (simplified)
        if (this.areFactsConflicting(fact1, fact2)) {
          conflicts.push({
            fact1: fact1.id,
            fact2: fact2.id,
            reason: 'Potential contradiction detected'
          });
        }
      }
    }
    
    return conflicts;
  }

  findRedundantFacts(facts) {
    // Simple redundancy detection
    const redundancies = [];
    
    for (let i = 0; i < facts.length; i++) {
      for (let j = i + 1; j < facts.length; j++) {
        const fact1 = facts[i];
        const fact2 = facts[j];
        
        if (this.areFactsSimilar(fact1, fact2)) {
          redundancies.push({
            fact1: fact1.id,
            fact2: fact2.id
          });
        }
      }
    }
    
    return redundancies;
  }

  areFactsConflicting(fact1, fact2) {
    // Simplified conflict detection
    const conflictPatterns = [
      { pattern1: /my name is (\w+)/i, pattern2: /my name is (\w+)/i },
      { pattern1: /i am (\d+) years old/i, pattern2: /i am (\d+) years old/i },
      { pattern1: /i live in (\w+)/i, pattern2: /i live in (\w+)/i }
    ];
    
    for (const { pattern1, pattern2 } of conflictPatterns) {
      const match1 = fact1.fact.match(pattern1);
      const match2 = fact2.fact.match(pattern2);
      
      if (match1 && match2 && match1[1] !== match2[1]) {
        return true;
      }
    }
    
    return false;
  }

  areFactsSimilar(fact1, fact2) {
    // Simple similarity check based on word overlap
    const words1 = new Set(fact1.fact.toLowerCase().split(/\W+/).filter(w => w.length > 2));
    const words2 = new Set(fact2.fact.toLowerCase().split(/\W+/).filter(w => w.length > 2));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    const similarity = intersection.size / union.size;
    
    // Also check for semantic similarity with key terms
    const keyTerms1 = fact1.fact.toLowerCase().match(/\b(work|software|engineer|employed|job|career)\b/g) || [];
    const keyTerms2 = fact2.fact.toLowerCase().match(/\b(work|software|engineer|employed|job|career)\b/g) || [];
    const hasCommonKeyTerms = keyTerms1.some(term => keyTerms2.includes(term));
    
    return similarity > 0.3 || (hasCommonKeyTerms && similarity > 0.2); // More flexible threshold
  }

  isComplexityConsistent(fact) {
    // Simple heuristics for complexity consistency
    const factLength = fact.fact.length;
    const answerLength = fact.answer.length;
    const questionComplexity = (fact.question.match(/\b(what|when|where|who)\b/gi) || []).length;
    
    if (fact.complexity === 'simple') {
      return factLength < 200 && answerLength < 50 && questionComplexity <= 2;
    } else {
      return factLength >= 100 || answerLength >= 20 || questionComplexity > 1;
    }
  }

  isAnswerLengthAppropriate(fact) {
    const answerLength = fact.answer.length;
    
    if (fact.complexity === 'simple') {
      return answerLength <= 100; // Simple facts should have concise answers
    } else {
      return answerLength >= 10; // Complex facts should have substantial answers
    }
  }

  isTruncatedMessage(content) {
    // Check for signs of truncation
    const truncationSigns = [
      /\.\.\.$/, // Ends with ellipsis
      /\[truncated\]/i,
      /\[cut off\]/i,
      /\bmaxim(um|al) length/i
    ];
    
    return truncationSigns.some(pattern => pattern.test(content));
  }
}

module.exports = {
  DataIntegrityChecker,
  DataIntegrityError
};